<script lang="ts" setup>
import type { V1LocationStudyCheckProjectIDUserIDGetResponseResult } from '@/api/api.model'
import { V1ManageTrainStudyRecordsPagePost } from '@/api/api.req'
import type { ICommonListItem } from '@/components/CommonList.vue'
import { TrainingProjectType, TrainingProjectTypeOptions, TrainingStatus } from '@/enums/training'
import { useDynamicList } from '@/hooks/useDynamicList'
import { useLoading } from '@/hooks/useLoading'
import { useRouteQuery } from '@/hooks/useRouteQuery'
import { currentUserInfo } from '@/store/sysUser'
import { getOptionLabel } from '@/utils'

const router = useRouter()

const query = useRouteQuery<{
  type: TrainingProjectType
  status: TrainingStatus[]
}>()

const projectTypeName = computed(() => getOptionLabel(TrainingProjectTypeOptions, query.type))

const title = computed(() => `${projectTypeName.value}列表`)

const trainingListState = useDynamicList({
  api: async (params) => {
    const resp = await V1ManageTrainStudyRecordsPagePost({
      ...params,
      data: {
        userId: currentUserInfo.value.id!,
        projectType: query.type as any,
        statusList: query.status,
      },
    })

    return {
      ...resp,
      records: resp.records?.map((item) => {
        return {
          ...item,
          id: item.id!,
          name: item.projectName!,
          tag: item.trainDuration
            ? `已训 ${item.trainDuration || 0} 分钟`
            : `待${projectTypeName.value}`,
          tagColor: item.trainDuration ? 'green' : 'yellow',
          linkText: '查看详情',
        } satisfies ICommonListItem
      }),
    }
  },
})

const fetchData = useLoading(() => {
  trainingListState.reset()
  trainingListState.load()
})

fetchData()

function onClickBeTrainingItem(item: V1LocationStudyCheckProjectIDUserIDGetResponseResult) {
  // 根据项目类型跳转到对应的详情页面
  let detailPath = '/training/detail'

  // projectType: 1-训练, 2-考核, 3-比赛
  switch (item.projectType) {
    case 1:
      detailPath = '/training/report-detail'
      break
    case 2:
      detailPath = '/training/exam-detail'
      break
    case 3:
      detailPath = '/training/competition-detail'
      break
    default:
      detailPath = '/training/detail'
  }

  router.push({
    path: detailPath,
    query: {
      id: item.id,
      userId: currentUserInfo.value.id,
    },
  })
}
</script>

<template>
  <VanNavBar :title="title" left-arrow left-text="返回" @click-left="$router.back()" />
  <div class="page-content page-content-padding">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <CardBox>
        <CommonList :list-state="trainingListState">
          <template #default="{ item }">
            <CommonDetailListItem
              :title="item.name"
              :tag="item.tag"
              :tagColor="item.tagColor"
              time="todo"
              :linkText="item.linkText"
              @click="onClickBeTrainingItem(item)"
            />
          </template>
        </CommonList>
      </CardBox>
    </VanPullRefresh>
  </div>
  <!-- <Footer /> -->
</template>

<style lang="less" scoped></style>
