<script lang="ts" setup>
import {
  V1EvaluateTaskOrderInfoIdPost,
  V1EvaluateTaskSubmitPost,
  V1ManageSysOrgUserListPost,
} from '@/api/api.req'
import { EvaluateResultOptions, EvaluateStatus, EvaluateStatusOptions } from '@/enums/evaluate'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useLoading } from '@/hooks/useLoading'
import { useRouteQuery } from '@/hooks/useRouteQuery'
import { getOptionLabel } from '@/utils'
import { uniq } from 'lodash-es'
import { showToast, type PickerColumn } from 'vant'
import type { FormExpose } from 'vant/es/form/types'

const query = useRouteQuery<{
  id?: string
  readonly?: '0' | '1'
}>()

const formRef = ref<FormExpose>()
const isReadonly = computed(() => query.readonly === '1')
const router = useRouter()

const state = reactive({
  data: {} as Record<string, string>,
  result: undefined as undefined | string,
  duration: undefined as undefined | number,
})

const detailApi = useAsyncData(async () => {
  const resp = await V1EvaluateTaskOrderInfoIdPost({ id: query.id! })

  const userIds = resp.reviewerList?.map((item) => item.userId!)
  const orgListResp = userIds?.length ? await V1ManageSysOrgUserListPost(uniq(userIds)) : []

  resp.resultList?.forEach((item) => {
    state.data[item.evaluateProject] = item.result?.toString()
  })

  state.result = resp.result?.toString()
  state.duration = resp.status?.toString() === EvaluateStatus.PENDING ? undefined : resp.duration

  return {
    ...resp,
    reviewerList: resp.reviewerList?.map((item) => ({
      ...item,
      orgList: orgListResp.find((n) => n.userId === item.userId)?.orgList,
    })),
  }
}, {} as any)

detailApi.load()

const data = computed(() => detailApi.data.value)

const ResultOptions: PickerColumn = EvaluateResultOptions.map((item) => ({
  ...item,
  text: item.label,
}))

const onSubmit = useLoading(async () => {
  await formRef.value?.validate()

  const _data = {
    id: data.value.id!,
    resultList: Object.entries(state.data).map((item) => ({
      evaluateProject: item[0],
      result: item[1],
    })),
    duration: 0,
    result: state.result,
  }

  await V1EvaluateTaskSubmitPost(_data as any)
  showToast(`评定完成`)
  router.back()
})
</script>

<template>
  <VanNavBar title="评定详情" left-arrow left-text="返回" @click-left="$router.back()" />
  <div class="page-content">
    <div class="title flex px-4 py-2">
      <div class="flex-1 truncate font-bold flex-1 w-0 truncate">{{ data?.evaluateName }}</div>
      <div class="right">
        <ColorTag
          :color="data.status?.toString() === EvaluateStatus.QUALIFIED ? 'green' : 'yellow'"
        >
          {{ getOptionLabel(EvaluateStatusOptions, data?.status?.toString()) }}
        </ColorTag>
      </div>
    </div>
    <VanForm ref="formRef">
      <div class="bg-white p-4 space-y-4 text-sm evaluate-form-content">
        <div class="row-section">
          <div class="row-title">评估对象</div>
          <div class="row-content">
            <span>{{ data?.assessorName }}</span>
            <span v-if="data.assessorOrgNames?.length">
              （{{ data.assessorOrgNames?.join('/') }}）
            </span>
          </div>
        </div>

        <div class="row-section">
          <div class="row-title">评估人员</div>
          <div class="row-content" v-for="item in data.reviewerList">
            <span>{{ item.userName }}</span>
            <span v-if="item.orgList?.length">
              （{{ item.orgList.map((org) => org.name).join('/') }}）
            </span>
          </div>
        </div>

        <div class="row-section">
          <div class="row-title">评估内容</div>
          <div class="row-content" v-for="(item, index) in data.resultList">
            <PickerField
              :key="index"
              :disabled="isReadonly"
              v-model="state.data[item.evaluateProject]"
              :label="item.evaluateProject"
              :columns="ResultOptions"
              :rules="[{ required: true, message: `请选择` }]"
            />
          </div>
          <div class="row-content">
            <VanField
              :disabled="isReadonly"
              label="时长（分钟）"
              v-model="state.duration"
              placeholder="请输入"
              type="number"
              :rules="[{ required: true, message: `请输入` }]"
            />
          </div>
        </div>

        <div class="row-section">
          <div class="row-title">评估结果</div>
          <div class="row-content">
            <PickerField
              :disabled="isReadonly"
              v-model="state.result"
              :rules="[{ required: true, message: `请选择` }]"
              :columns="ResultOptions"
            />
          </div>
        </div>
      </div>
    </VanForm>
    <div class="h-80px"></div>
  </div>

  <FixedButton
    @click="onSubmit"
    type="primary"
    :disabled="isReadonly"
    :loading="onSubmit.isLoading"
  >
    提交
  </FixedButton>
</template>

<style lang="less" scoped>
.row-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 16px;
  border-bottom: 0.5px solid #e7e7e7;

  &:last-child {
    padding-bottom: 0;
    border-bottom: 0.5px solid transparent;
  }

  .row-title {
    font-size: 14px;
  }

  .row-content {
    color: #a6a6a6;
  }
}

:deep(.evaluate-form-content) {
  .row-content {
    .van-cell {
      padding: 0;
      &::after {
        content: unset;
      }
    }
  }
}
</style>
