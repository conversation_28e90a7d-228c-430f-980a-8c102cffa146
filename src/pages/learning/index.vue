<script lang="ts">
export enum LearningTabId {
  PENDING = 'pending',
  LEARNED = 'learned',
}
</script>

<script lang="ts" setup>
import Footer from '@/components/layOut/footer.vue'
import { useLoading } from '@/hooks/useLoading'
import { useDynamicList } from '@/hooks/useDynamicList'
import { V1ManageUserLearningsPagePost, V1ManageUserLearningsStatistic } from '@/api/api.req'
import type {
  V1ManageUserLearningsLearningDetailIDGetResponseResult,
  V1ManageUserLearningsPagePostRequestBodyData,
  V1ManageUserLearningsPagePostResponseResult,
} from '@/api/api.model'
import { LearningStatus, LearningStatusOptions } from '@/enums/learning'
import type { ListTabItem } from '@/components/CommonTabList.vue'
import { getOptionLabel } from '@/utils'
import type { IconStatisticsProps } from '@/components/IconStatistics.vue'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useRouteQuery } from '@/hooks/useRouteQuery'

const router = useRouter()

const query = useRouteQuery<{
  tab?: LearningTabId
}>()

const baseInfoApi = useAsyncData(V1ManageUserLearningsStatistic, {})

const infos = computed(() => {
  const data = baseInfoApi.data.value || {}

  const configs: IconStatisticsProps[] = [
    {
      icon: 'svg/item/FileText',
      title: '待学习',
      value: data.waitingNum,
      unit: '课',
      layout: 'horizontal',
    },
    {
      icon: 'svg/item/FileDone',
      title: '已学习',
      value: data.finishedNum,
      unit: '课',
      layout: 'horizontal',
    },
  ]

  return configs
})

const pendingLearningListState = useDynamicList({
  api: async (params) => {
    const _params = {
      ...params,
      data: {
        statusList: [LearningStatus.LEARNING, LearningStatus.WAITING],
      } as V1ManageUserLearningsPagePostRequestBodyData,
    }
    return convertToListItems(await V1ManageUserLearningsPagePost(_params))
  },
})

const learnedListState = useDynamicList({
  api: async (params) => {
    const _params = {
      ...params,
      data: {
        statusList: [LearningStatus.FINISH],
      } as V1ManageUserLearningsPagePostRequestBodyData,
    }
    return convertToListItems(await V1ManageUserLearningsPagePost(_params))
  },
})

function convertToListItems(result: V1ManageUserLearningsPagePostResponseResult) {
  return {
    ...result,
    records: result.records?.map((item) => {
      return {
        ...item,
        id: item.id!,
        name: item.name!,
        tag: getOptionLabel(LearningStatusOptions, item.status) || item.status!,
        linkText: item.status === LearningStatus.FINISH ? '去查看' : '去学习',
      }
    }),
  }
}

const currentListState = computed(() => {
  return tabsState.activeTabId === 'pending' ? pendingLearningListState : learnedListState
})

const tabsConfig = [
  { id: LearningTabId.PENDING, name: '待学习' },
  { id: LearningTabId.LEARNED, name: '已学习' },
]

const tabsState = reactive({
  activeTabId: query.tab ?? LearningTabId.PENDING,
})

const fetchData = useLoading(_fetchData)

fetchData()

function _fetchData() {
  baseInfoApi.load()
  currentListState.value.reset()
  currentListState.value.load()
}

function gotoViewerPage(item: V1ManageUserLearningsLearningDetailIDGetResponseResult) {
  router.push({
    path: '/learning/detail',
    query: {
      id: item.id,
    },
  })
}

function onClickListItem(item: V1ManageUserLearningsLearningDetailIDGetResponseResult) {
  // 所有状态都先跳转到学习详情页面
  gotoViewerPage(item)
}

function onTabChanged(tabItem: ListTabItem) {
  query.tab = tabItem.id as LearningTabId
  tabsState.activeTabId = tabItem.id as LearningTabId
}
</script>

<template>
  <VanNavBar title="学习" />
  <div class="page-content page-content-padding">
    <VanPullRefresh
      :model-value="fetchData.isLoading"
      success-text="刷新成功!"
      @refresh="fetchData"
    >
      <CardBox class="flex justify-around mb-4">
        <IconStatistics v-for="info in infos" v-bind="info" />
      </CardBox>

      <CardBox class="pt-0!">
        <CommonTabList
          :tabs="tabsConfig"
          :active-tab-id="tabsState.activeTabId"
          @change="onTabChanged"
        />
        <CommonList :list-state="currentListState" @click-item="onClickListItem" />
      </CardBox>
    </VanPullRefresh>
  </div>
  <Footer />
</template>

<style lang="less" scoped>
.title {
  @apply mb-2 font-bold;
  color: var(--text-icon-font-gy-190, rgba(0, 0, 0, 0.9));
}

.list {
  @apply flex flex-col gap-2 mt-2;
}
</style>
