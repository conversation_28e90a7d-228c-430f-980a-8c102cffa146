<script lang="ts" setup>
import { showConfirmDialog } from 'vant'
import TestPaperQuestionsRender from '@/pages/components/test-paper-questions-render.vue'
import {
  type TestPaperQuestionsGroupCell,
  isBlank,
  splitGroups,
  testPaperQuestionsRenderOtherProps,
  verifyIsFillBlank,
  verifyIsMultip,
} from '@/pages/components/const'
import { QUESTION_TYPE, TEST_PAPER_MODE } from '@/config'
import {
  V1ManageQuestionBankColumnGetQuestionBankColumnListPost,
  V1MobileHomeExamRecordPaperInitId,
  V1MobileStudyOverPost,
} from '@/api/api.req'
import ScoreDialog from '@/pages/exam/score-dialog.vue'
import type {
  ExamRecordElement,
  PaperElement,
  QuestionBankColumnDTOElement,
  V1MobileStudyOverPostResponseResult,
} from '@/api/api.model'
import { secondsToHMS } from '@/utils'
import Fields from '@/components/Fields.vue'

const router = useRouter()

const route = useRoute()

const examResDialogProps = reactive({
  dialogShow: false,
  examRes: {
    time: '',
    score: 0,
  },
})

const testPaperQuestions = ref<TestPaperQuestionsGroupCell[]>([])

const testPaperInfo = ref<PaperElement>()

const examRecord = ref<ExamRecordElement>()

const id = computed(() => route.query.id as string)

const loading = ref<boolean>(true)

const questionBankColumn = ref<QuestionBankColumnDTOElement[]>([])

/**
 * 加载所有题库字段配置
 */
async function loadAllQuestionBankColumn() {
  const resp = await V1ManageQuestionBankColumnGetQuestionBankColumnListPost({})

  questionBankColumn.value = resp || []
}

async function loadQuestions() {
  loading.value = true
  try {
    const resp = await V1MobileHomeExamRecordPaperInitId({ id: id.value })
    const qs = resp?.paper?.questionList || []
    const questionList = qs.map((item) => {
      return {
        ...item,
        userAnswer:
          verifyIsFillBlank(item.questionType!) || item.questionType === QUESTION_TYPE.SHORT_ANSWER
            ? Array(item.questionOption?.length || 0).fill('')
            : verifyIsMultip(item.questionType!)
              ? []
              : undefined,
      }
    })
    testPaperQuestions.value = splitGroups(questionList)

    testPaperInfo.value = resp?.paper?.paper || {}
    examRecord.value = resp?.examRecord || {}
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

function verifyAllBispose() {
  return testPaperQuestions.value.every((group) => {
    if (verifyIsFillBlank(group.qType!)) {
      return group.questions.every(
        (q) => Array.isArray(q.userAnswer) && q.userAnswer.every((v) => !isBlank(v)),
      )
    }
    if (verifyIsMultip(group.qType!)) {
      return group.questions.every((q) => Array.isArray(q.userAnswer) && q.userAnswer.length)
    } else {
      return group.questions.every((q) => !isBlank(q.userAnswer))
    }
  })
}

async function onSubmit() {
  await showConfirmDialog({
    title: verifyAllBispose() ? '确认提交吗？' : '未完全作答，确认提交吗？',
    className: 'warn-dialog',
  })

  const answerList = testPaperQuestions.value.reduce(
    (iter, group) => {
      const groupAnswers = group.questions.map((item) => {
        return {
          id: item.id!,
          answer:
            verifyIsFillBlank(item.questionType!) ||
            item.questionType === QUESTION_TYPE.SHORT_ANSWER ||
            verifyIsMultip(item.questionType!)
              ? item.userAnswer
              : isBlank(item.userAnswer)
                ? []
                : [item.userAnswer],
        }
      })
      return [...iter, ...groupAnswers]
    },
    [] as { id: string; answer: string }[],
  )

  const resp = await V1MobileStudyOverPost({
    id: id.value,
    answerList,
  })

  showDialog(resp)
}

function showDialog(resp: V1MobileStudyOverPostResponseResult) {
  examResDialogProps.examRes.score = resp.score!
  examResDialogProps.examRes.time = secondsToHMS(resp.duration!)
  examResDialogProps.dialogShow = true
}

function goBack() {
  router.back()
}

function openViewAnswers() {
  router.replace({
    path: '/exam/view-answers',
    query: {
      id: id.value,
    },
  })
}

const testPaperInfoFields = computed(() => {
  const _propertyList = testPaperInfo.value?.questionPropertyList || []

  return _propertyList.map((o) => {
    const label =
      questionBankColumn.value.find((col) => col.columnKey === o.columnKey)?.columnName || '-'
    return {
      label,
      value: o.columnValue,
    }
  })
})

onMounted(() => {
  loadQuestions()
  loadAllQuestionBankColumn()
})
</script>

<template>
  <VanNavBar
    :title="testPaperInfo?.name || '试卷名称'"
    left-text="返回"
    left-arrow
    @click-left="goBack"
  />
  <div v-if="loading" class="h-200px flex items-center justify-center">
    <VanLoading type="spinner" />
  </div>
  <div v-else class="box-border h-100vh overflow-y-auto px-4 pt-4 pb-120px">
    <div class="overflow-hidden rounded-8px bg-[#fff]">
      <Fields :fields="testPaperInfoFields" />
    </div>
    <TestPaperQuestionsRender
      v-model:test-paper-value="testPaperQuestions"
      v-bind="testPaperQuestionsRenderOtherProps(TEST_PAPER_MODE.ONLINE_WIRTE)"
      :question-type-list="testPaperInfo?.questionTypeList || []"
    />
  </div>
  <div class="fixed-bottom">
    <VanButton type="primary" block @click="onSubmit"> 提交 </VanButton>
  </div>
  <ScoreDialog
    v-model:visible="examResDialogProps.dialogShow"
    v-bind="examResDialogProps"
    @confirm="openViewAnswers"
    @cancel="goBack"
  />
</template>

<style lang="less" scoped>
.fixed-bottom {
  @apply fixed bottom-0 w-full border-1px border-color-[#f4f6f8] border-t-solid bg-[#fff] px-8px py-10px box-border;
}
:deep(.van-field__control:disabled) {
  color: #333 !important;
  -webkit-text-fill-color: #333;
}
:deep(.van-field__label) {
  color: #666 !important;
}
</style>
