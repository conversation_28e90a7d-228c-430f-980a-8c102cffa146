<script lang="ts" setup>
import ColorTag, { ColorTagColor, type ColorTagColorType } from './ColorTag.vue'

export interface CommonDetailListItemProps {
  title: string
  tag?: string
  tagColor?: ColorTagColorType
  time?: string | number
  linkText?: string
}

defineProps<CommonDetailListItemProps>()
</script>

<template>
  <div class="common-detail-list-item">
    <div class="header">
      <div class="title flex-1 w-0">{{ title }}</div>
      <div v-if="linkText" class="detail">
        <span class="detail-text">{{ linkText }}</span>
        <VanIcon name="arrow" />
      </div>
    </div>

    <div class="footer">
      <ColorTag v-if="tag" :color="tagColor || ColorTagColor.Green">
        {{ tag }}
      </ColorTag>
      <div v-if="time" class="time">
        {{ time }}
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.common-detail-list-item {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px 0;
  width: 100%;

  border-bottom: 0.5px solid #ddd;

  &:first-child {
    padding-top: 0;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .title {
      font-size: 14px;
      color: #000000;
      line-height: 22px;
      font-weight: 500;
    }

    .detail {
      display: flex;
      align-items: center;
      gap: 2px;
      color: #999999;

      .detail-text {
        font-size: 11px;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    gap: 4px;

    .time {
      font-size: 12px;
      color: #666666;
      line-height: 22px;
      white-space: nowrap;
    }
  }
}
</style>
